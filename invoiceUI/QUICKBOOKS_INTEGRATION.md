# QuickBooks Integration Form

## Overview
The QuickBooks integration form allows users to connect their QuickBooks Online account to the invoice processing system. When the "Connect QuickBooks" button is clicked, a modal form appears with all the necessary fields to establish the connection.

## Form Fields

The form includes the following fields that match the required API payload:

### Required Fields
1. **Company ID (Realm ID)** - `realm_id`
   - The QuickBooks company identifier
   - Type: Text input
   - Required: Yes

2. **Client ID** - `client_id`
   - QuickBooks application client ID
   - Type: Text input
   - Required: Yes

3. **Client Secret** - `client_secret`
   - QuickBooks application client secret
   - Type: Password input (hidden)
   - Required: Yes

4. **Access Token** - `access_token`
   - OAuth access token for API calls
   - Type: Password input (hidden)
   - Required: Yes

5. **Refresh Token** - `refresh_token`
   - OAuth refresh token for token renewal
   - Type: Password input (hidden)
   - Required: Yes

6. **Token Expires At** - `token_expires_at`
   - Token expiration date and time
   - Type: DateTime-local input
   - Format: ISO 8601 datetime string
   - Required: Yes

7. **Environment** - `environment`
   - QuickBooks environment (sandbox or production)
   - Type: Select dropdown
   - Options: "sandbox", "production"
   - Default: "sandbox"
   - Required: Yes

## API Integration

### Payload Structure
When the form is submitted, it sends the following JSON payload:

```json
{
  "realm_id": "9341455012370538",
  "client_id": "ABUrtQN3bOciG1SRaCJJnJAs1cMc1vWkgV3EbRjw4GwVaE6W0x",
  "client_secret": "70DDjvCg9RJ9KjgrNpmODtbgxLNnPF5IJIhqTnBb",
  "access_token": "eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9",
  "refresh_token": "RT1-38-H0-1762602646zekkqvqrwkehy7w15l8m",
  "token_expires_at": "2025-08-31T04:53:13.392Z",
  "environment": "sandbox"
}
```

### API Endpoints

#### Connect QuickBooks
- **URL**: `/api/v1/quickbooks/connections`
- **Method**: POST
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>` (automatically added)

#### Get Connections List
- **URL**: `/api/v1/quickbooks/connections`
- **Method**: GET
- **Headers**:
  - `Authorization: Bearer <token>` (automatically added)
- **Response**: Returns a list of all QuickBooks connections

### Response Handling
The form handles both success and error responses:

#### Success Response
```json
{
  "success": true,
  "message": "QuickBooks connection already exists",
  "connection_status": {
    "is_connected": true,
    "realm_id": "9341455012370538",
    "environment": "sandbox",
    "token_expires_at": "2025-08-31T04:53:13.392000Z",
    "token_status": "valid",
    "last_tested": null,
    "company_name": null,
    "company_id": null
  },
  "timestamp": "2025-07-31T04:54:34.173743"
}
```

#### GET Response (Connections List)
```json
{
  "success": true,
  "message": "Retrieved 2 connections",
  "connections": [
    {
      "id": "688a077daede0f37c10de2a3",
      "realm_id": "9341455012370538",
      "client_id": "ABUrtQN3bOciG1SRaCJJnJAs1cMc1vWkgV3EbRjw4GwVaE6W0x",
      "environment": "sandbox",
      "token_expires_at": "2025-07-31T06:22:30.162000",
      "created_at": "2025-07-30T11:52:29.498000",
      "updated_at": "2025-07-31T05:22:30.163000",
      "last_tested": null,
      "is_active": true,
      "company_info": null
    }
  ],
  "pagination": {
    "total": 2,
    "skip": 0,
    "limit": 50,
    "has_more": false
  }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Invalid credentials or connection failed",
  "detail": "Additional error details if available"
}
```

## User Experience Features

### Loading State
- Shows a spinner and "Connecting..." text while the API call is in progress
- Disables form buttons during connection attempt

### Error Handling
- Displays error messages in a red alert box below the form
- Validates that all required fields are filled before submission
- Clears errors when the dialog is reopened

### Form Validation
- Client-side validation ensures all required fields are completed
- Shows appropriate error messages for missing or invalid data

### Security
- Sensitive fields (client_secret, access_token, refresh_token) use password input type
- Form data is cleared after successful connection

## File Locations

### Frontend Components
- **Main Form**: `invoiceUI/src/pages/Admin.tsx` (lines 992-1127)
- **API Functions**: `invoiceUI/src/utils/api.ts` (connectQuickBooks function)
- **TypeScript Interfaces**: `invoiceUI/src/utils/api.ts` (QuickBooksConnectionData, etc.)

### Key Functions
- `handleQBConnect()` - Handles form submission and API call
- `connectQuickBooks()` - API utility function for the connection request
- Form state management with React hooks

## Usage Instructions

1. Navigate to the Admin page
2. Go to the "Accounting Integrations" tab
3. Click "Connect QuickBooks" button
4. Fill in all required fields with your QuickBooks credentials
5. Select the appropriate environment (sandbox/production)
6. Click "Connect QuickBooks" to submit the form
7. Wait for the connection to be established
8. The form will close automatically on success, or show an error message if the connection fails

## Testing

To test the integration:
1. Start the development server: `npm run dev`
2. Navigate to http://localhost:5175/
3. Go to Admin → Accounting Integrations
4. Test the form with valid/invalid credentials
5. Verify error handling and loading states work correctly
