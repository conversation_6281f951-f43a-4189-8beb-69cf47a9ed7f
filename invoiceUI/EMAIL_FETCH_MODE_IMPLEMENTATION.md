# Email Fetch Mode Toggle Implementation

## Overview

This document describes the implementation of the email fetch mode toggle button in the Connect Outlook dialog box. The toggle allows users to choose between fetching only new emails or all existing emails when connecting their Outlook account.

## Implementation Details

### 1. API Changes

**File: `src/utils/api.ts`**

Updated the `getOutlookAuthUrl` function to accept an `emailFetchMode` parameter:

```typescript
export const getOutlookAuthUrl = async (
  openBrowser: boolean = true, 
  emailFetchMode: 'new_only' | 'all_emails' = 'new_only'
): Promise<OutlookAuthResponse> => {
  // ... implementation
  const response = await makeAuthenticatedRequest(
    `/api/v1/outlook/auth/url?open_browser=${openBrowser}&email_fetch_mode=${emailFetchMode}`,
    // ... rest of the implementation
  )
}
```

### 2. UI Changes

**File: `src/pages/Admin.tsx`**

#### Added State Management
- Added `fetchAllEmails` state variable to track toggle position
- Default value is `false` (new_only mode)

#### Added Toggle Button
- Positioned between the feature list and error message in the dialog
- Uses the `Switch` component from the UI library
- Includes descriptive labels and explanatory text

#### Updated Dialog Behavior
- Toggle resets to default (false) when dialog is closed
- Toggle state is passed to the API call when connecting

### 3. Toggle Behavior

| Toggle Position | Value Sent to API | Description |
|----------------|-------------------|-------------|
| OFF (default)  | `new_only`        | Fetch only emails received after connection |
| ON             | `all_emails`      | Fetch all existing emails from mailbox |

### 4. User Experience

1. **Default State**: Toggle is OFF, showing "Fetch only new emails received after connection"
2. **Toggle ON**: Shows "Fetch all existing emails from your mailbox"
3. **Visual Feedback**: Clear labels on both sides of the toggle ("New only" / "All emails")
4. **Reset Behavior**: Toggle automatically resets when dialog is closed or cancelled

### 5. API Integration

The implementation correctly integrates with the provided API endpoint:

```bash
curl -X 'POST' \
  'http://127.0.0.1:8000/api/v1/outlook/auth/url?open_browser=true&email_fetch_mode=new_only' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer {token}' \
  -d ''
```

When toggle is OFF: `email_fetch_mode=new_only`
When toggle is ON: `email_fetch_mode=all_emails`

### 6. Code Structure

The implementation follows the existing patterns in the codebase:
- Uses existing UI components (Switch, Dialog)
- Maintains consistent styling and spacing
- Follows the established error handling patterns
- Integrates seamlessly with the existing OAuth flow

### 7. Testing

To test the implementation:
1. Navigate to the Admin page
2. Click "Connect Outlook" button
3. Observe the toggle button in the dialog
4. Toggle between states and observe the description changes
5. Connect with different toggle states to verify API calls

### 8. Future Enhancements

Potential improvements for future versions:
- Add tooltips explaining the difference between modes
- Show estimated processing time for each mode
- Add confirmation dialog for "all emails" mode if mailbox is large
- Persist user preference for future connections
