{"hash": "d855bfe6", "configHash": "f8d65400", "lockfileHash": "7dfa0731", "browserHash": "63379799", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "6971be29", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2d595312", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8a4c01e3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4a6868eb", "needsInterop": true}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "ea1de6b9", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "00850dd3", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "7763ae55", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "b8a13155", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "301d948c", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "f34db766", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "e8aa26dd", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "1c38b210", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "f3f91a8c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "de86081e", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "520eda42", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "300fa1c2", "needsInterop": true}, "react-pdf": {"src": "../../react-pdf/dist/index.js", "file": "react-pdf.js", "fileHash": "4b03740e", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "4e326a18", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "8b6ad91b", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "3cb4fca5", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "c3c72497", "needsInterop": false}}, "chunks": {"chunk-XUSVWCLU": {"file": "chunk-XUSVWCLU.js"}, "chunk-VKEQMGGX": {"file": "chunk-VKEQMGGX.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-YDJLXKPV": {"file": "chunk-YDJLXKPV.js"}, "chunk-MFAJADHG": {"file": "chunk-MFAJADHG.js"}, "chunk-MJHCO6RN": {"file": "chunk-MJHCO6RN.js"}, "chunk-7YYRKW5B": {"file": "chunk-7YYRKW5B.js"}, "chunk-PIINO46W": {"file": "chunk-PIINO46W.js"}, "chunk-CVM3PBKH": {"file": "chunk-CVM3PBKH.js"}, "chunk-DRMTRIGX": {"file": "chunk-DRMTRIGX.js"}, "chunk-RPCDYKBN": {"file": "chunk-RPCDYKBN.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}