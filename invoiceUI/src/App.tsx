import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'sonner'
import Dashboard from './pages/Dashboard'
import Invoices from './pages/Invoices'
import InvoiceDetail from './pages/InvoiceDetail'
import Admin from './pages/Admin'
import Login from './pages/Login'
import Signup from './pages/Signup'
import TestViewer from './pages/TestViewer'
import TestPDFViewer from './pages/TestPDFViewer'
import SimplePDFTest from './pages/SimplePDFTest'
import './App.css'

function App() {
  return (
    <>
      <Router>
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/invoices" element={<Invoices />} />
          <Route path="/invoices/:id" element={<InvoiceDetail />} />
          <Route path="/admin" element={<Admin />} />
          <Route path="/test-viewer" element={<TestViewer />} />
          <Route path="/test-pdf" element={<TestPDFViewer />} />
          <Route path="/simple-pdf" element={<SimplePDFTest />} />
        </Routes>
      </Router>
      <Toaster position="top-right" richColors />
    </>
  )
}

export default App
