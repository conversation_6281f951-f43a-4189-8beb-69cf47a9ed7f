import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

/**
 * Simple test component to debug QuickBooks form issues
 * This component isolates the form to identify what's causing redirects
 */
export const QuickBooksTestForm: React.FC = () => {
  const [showDialog, setShowDialog] = useState(false)
  const [formData, setFormData] = useState({
    realm_id: "",
    client_id: "",
    client_secret: "",
    access_token: "",
    refresh_token: "",
    token_expires_at: "",
    environment: "sandbox",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('Form submit handler called')
    e.preventDefault()
    e.stopPropagation()
    
    setIsSubmitting(true)
    setError("")
    
    try {
      // Simulate API call
      console.log('Submitting form data:', formData)
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('Form submission completed')
      setShowDialog(false)
      
      // Reset form
      setFormData({
        realm_id: "",
        client_id: "",
        client_secret: "",
        access_token: "",
        refresh_token: "",
        token_expires_at: "",
        environment: "sandbox",
      })
    } catch (err) {
      console.error('Form submission error:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
    
    return false
  }

  const handleInputChange = (field: string, value: string) => {
    console.log(`Input changed: ${field} = ${value}`)
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleOpenDialog = (e: React.MouseEvent) => {
    console.log('Open dialog button clicked')
    e.preventDefault()
    e.stopPropagation()
    setError("")
    setShowDialog(true)
  }

  const handleCloseDialog = (open: boolean) => {
    console.log('Dialog onOpenChange called with:', open)
    if (!isSubmitting) {
      setShowDialog(open)
      if (!open) {
        setError("")
      }
    }
  }

  return (
    <div className="p-4 border rounded-lg bg-white">
      <h3 className="text-lg font-medium mb-4">QuickBooks Test Form</h3>
      <p className="text-sm text-gray-600 mb-4">
        This is a test form to debug the QuickBooks connection issue.
      </p>
      
      <Dialog open={showDialog} onOpenChange={handleCloseDialog}>
        <DialogTrigger asChild>
          <Button 
            onClick={handleOpenDialog}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Test QuickBooks Form
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Test QuickBooks Connection</DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="test_realm_id">Company ID (Realm ID)</Label>
              <Input
                id="test_realm_id"
                type="text"
                autoComplete="off"
                value={formData.realm_id}
                onChange={(e) => handleInputChange('realm_id', e.target.value)}
                placeholder="Enter your QuickBooks Company ID"
              />
            </div>
            
            <div>
              <Label htmlFor="test_client_id">Client ID</Label>
              <Input
                id="test_client_id"
                type="text"
                autoComplete="off"
                value={formData.client_id}
                onChange={(e) => handleInputChange('client_id', e.target.value)}
                placeholder="Enter your QuickBooks Client ID"
              />
            </div>
            
            <div>
              <Label htmlFor="test_client_secret">Client Secret</Label>
              <Input
                id="test_client_secret"
                type="password"
                autoComplete="new-password"
                value={formData.client_secret}
                onChange={(e) => handleInputChange('client_secret', e.target.value)}
                placeholder="Enter your QuickBooks Client Secret"
              />
            </div>
            
            <div>
              <Label htmlFor="test_access_token">Access Token</Label>
              <Input
                id="test_access_token"
                type="password"
                autoComplete="new-password"
                value={formData.access_token}
                onChange={(e) => handleInputChange('access_token', e.target.value)}
                placeholder="Enter access token"
              />
            </div>
            
            <div>
              <Label htmlFor="test_refresh_token">Refresh Token</Label>
              <Input
                id="test_refresh_token"
                type="password"
                autoComplete="new-password"
                value={formData.refresh_token}
                onChange={(e) => handleInputChange('refresh_token', e.target.value)}
                placeholder="Enter refresh token"
              />
            </div>
            
            <div>
              <Label htmlFor="test_token_expires_at">Token Expires At</Label>
              <Input
                id="test_token_expires_at"
                type="datetime-local"
                autoComplete="off"
                value={formData.token_expires_at}
                onChange={(e) => handleInputChange('token_expires_at', e.target.value)}
                placeholder="Token expiration date and time"
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button"
                variant="outline" 
                onClick={(e) => {
                  e.preventDefault()
                  setShowDialog(false)
                }} 
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-green-600 hover:bg-green-700"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Testing...
                  </>
                ) : (
                  'Test Connection'
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
