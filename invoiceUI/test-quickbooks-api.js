/**
 * Test script for QuickBooks API integration
 * This script demonstrates how to test the QuickBooks connection API
 */

const API_BASE_URL = 'http://127.0.0.1:8000';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************.FhJToo5UQqbhA7fVJCRSdbzagLYA9vhF6TJMh6i8E7A';

// Test payload matching the form structure
const testPayload = {
  "realm_id": "9341455012370538",
  "client_id": "ABUrtQN3bOciG1SRaCJJnJAs1cMc1vWkgV3EbRjw4GwVaE6W0x",
  "client_secret": "70DDjvCg9RJ9KjgrNpmODtbgxLNnPF5IJIhqTnBb",
  "access_token": "eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9",
  "refresh_token": "RT1-38-H0-1762602646zekkqvqrwkehy7w15l8m",
  "token_expires_at": "2025-08-31T04:53:13.392Z",
  "environment": "sandbox"
};

/**
 * Test the QuickBooks connection API
 */
async function testQuickBooksConnection() {
  try {
    console.log('Testing QuickBooks Connection API...');
    console.log('Endpoint:', `${API_BASE_URL}/api/v1/quickbooks/connections`);
    console.log('Payload:', JSON.stringify(testPayload, null, 2));
    
    const response = await fetch(`${API_BASE_URL}/api/v1/quickbooks/connections`, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const data = await response.json();
    
    console.log('\n--- Response ---');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('\n✅ Connection successful!');
      console.log('Connection Status:', data.connection_status.is_connected ? 'Connected' : 'Not Connected');
      console.log('Token Status:', data.connection_status.token_status);
      console.log('Environment:', data.connection_status.environment);
      console.log('Realm ID:', data.connection_status.realm_id);
    } else {
      console.log('\n❌ Connection failed:', data.message);
    }
    
  } catch (error) {
    console.error('\n❌ Error testing connection:', error.message);
  }
}

/**
 * Test getting existing connection status
 */
async function testGetConnectionStatus() {
  try {
    console.log('\n\nTesting Get Connection Status API...');
    console.log('Endpoint:', `${API_BASE_URL}/api/v1/quickbooks/connections`);
    
    const response = await fetch(`${API_BASE_URL}/api/v1/quickbooks/connections`, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      }
    });

    const data = await response.json();
    
    console.log('\n--- Get Status Response ---');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('\n✅ Status retrieved successfully!');
      console.log('Total Connections:', data.connections.length);

      if (data.connections.length > 0) {
        const activeConnection = data.connections.find(conn => conn.is_active) || data.connections[0];
        console.log('Active Connection:');
        console.log('  - Realm ID:', activeConnection.realm_id);
        console.log('  - Environment:', activeConnection.environment);
        console.log('  - Is Active:', activeConnection.is_active);
        console.log('  - Token Expires:', activeConnection.token_expires_at);
        console.log('  - Last Tested:', activeConnection.last_tested || 'Never');
      } else {
        console.log('No connections found');
      }
    } else {
      console.log('\n❌ Failed to get status:', data.message);
    }
    
  } catch (error) {
    console.error('\n❌ Error getting status:', error.message);
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting QuickBooks API Tests\n');
  console.log('='.repeat(50));
  
  // Test connection
  await testQuickBooksConnection();
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test getting status
  await testGetConnectionStatus();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Tests completed!');
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  runTests();
} else {
  // Browser environment - expose functions to global scope
  window.testQuickBooksConnection = testQuickBooksConnection;
  window.testGetConnectionStatus = testGetConnectionStatus;
  window.runTests = runTests;
  console.log('QuickBooks API test functions loaded. Run runTests() to start testing.');
}
